import { useState, useEffect } from 'react';
import { useRouter } from 'next/router';
import Head from 'next/head';
import Link from 'next/link';
import { useAuth } from '@/contexts/AuthContext';
import ProtectedRoute from '@/components/admin/ProtectedRoute';
import AdminLayout from '@/components/admin/AdminLayout';
// import AvailabilityCalendarCard from '@/components/admin/AvailabilityCalendarCard'; // Replaced
import AvailabilitySettings from '@/components/admin/AvailabilitySettings'; // New Import
import PerformanceMetricsCard from '@/components/admin/PerformanceMetricsCard';
import QuickActionsCard from '@/components/admin/QuickActionsCard';
import { toast } from 'react-toastify';
import styles from '@/styles/admin/ArtistBraiderDashboard.module.css';

// Placeholder for BookingListCard - will be created later
import BookingListCard from '@/components/admin/BookingListCard'; // Import BookingListCard


export default function ArtistBraiderDashboard() {
  const { user, loading: authLoading, supabaseClient, signOut } = useAuth(); // Add signOut from useAuth
  const router = useRouter();
  const [profile, setProfile] = useState(null);
  const [dashboardData, setDashboardData] = useState(null); // To store all data from dashboard-enhanced
  const [loading, setLoading] = useState(true); // For dashboard data loading specifically
  const [error, setError] = useState(null)
  const [isFirstTimeLogin, setIsFirstTimeLogin] = useState(false) // Track first-time login

  useEffect(() => {
    if (!authLoading && user) {
      // Check if this is a first-time login by looking for activation metadata
      const isFirstTime = user.user_metadata?.account_activated &&
                         !localStorage.getItem(`oss_dashboard_visited_${user.id}`)
      setIsFirstTimeLogin(isFirstTime)

      fetchDashboardData();
    } else if (!authLoading && !user) {
      router.push('/staff-login?redirect=/admin/artist-braider-dashboard');
    }
  }, [user, authLoading, router]); // Add router to dependency array

  const fetchDashboardData = async () => {
    if (!user || !user.access_token) {
      console.log("[ArtistDashboard] No user or access token, skipping fetch.");
      setLoading(false); // Stop loading if no user/token
      if (!authLoading) router.push('/staff-login?redirect=/admin/artist-braider-dashboard');
      return;
    }

    try {
      setLoading(true);
      setError(null);
      console.log('[ArtistDashboard] Fetching dashboard data...');
      const response = await fetch('/api/artist/dashboard-enhanced', {
        headers: {
          'Authorization': `Bearer ${user.access_token}`,
          'Content-Type': 'application/json'
        }
      });

      if (!response.ok) {
        const errData = await response.json();
        if (response.status === 403) {
          setError('Access denied. This dashboard is only available for artists and braiders.');
          toast.error('Access Denied.');
        } else if (response.status === 404 && errData.error === 'Artist profile not found') {
            // This specific case might happen if the artist_profile record is missing after login
            // This can happen if `review.js` or `activate-account.js` failed to create it.
            // Or, if `is_profile_complete` is not yet set by the `review` function.
            console.warn('[ArtistDashboard] Artist profile not found via API, redirecting to complete profile.');
            toast.info('Please complete your profile to continue.');
            router.push('/admin/complete-profile');
            return; // Important to return here
        }
        else {
          setError(errData.error || 'Failed to fetch dashboard data');
          toast.error(errData.error || 'Failed to fetch dashboard data');
        }
        setLoading(false);
        return;
      }

      const data = await response.json();
      console.log('[ArtistDashboard] Dashboard data received:', data);

      // Check profile completion status
      const profileIncomplete = !data.profile ||
                               data.profile.is_profile_complete === false ||
                               (typeof data.profile.is_profile_complete === 'undefined' &&
                                (!data.profile.artist_name || !data.profile.display_name))

      if (profileIncomplete) {
        console.log('[ArtistDashboard] Profile incomplete, redirecting to /admin/complete-profile');

        // Show different messages for first-time vs returning users
        if (isFirstTimeLogin) {
          toast.success('Welcome! Let\'s complete your profile to get started.');
        } else {
          toast.info('Please complete your profile to continue.');
        }

        router.push('/admin/complete-profile');
        setLoading(false);
        return;
      }

      // Mark that user has visited dashboard (for first-time detection)
      if (isFirstTimeLogin && user?.id) {
        localStorage.setItem(`oss_dashboard_visited_${user.id}`, 'true')
        setIsFirstTimeLogin(false)
      }

      setProfile(data.profile);
      setDashboardData(data); // Store all fetched data

    } catch (error) {
      console.error('[ArtistDashboard] Error fetching dashboard data:', error);
      setError('Failed to load dashboard data. Please try again.');
      toast.error('Failed to load dashboard data');
    } finally {
      setLoading(false);
    }
  };

  // This function might not be directly needed if ProfileManagementCard is on a separate page
  // or if AvailabilitySettings handles its own data refreshes.
  // const handleProfileUpdate = () => {
  //   fetchDashboardData();
  // };

  // This can be removed if AvailabilitySettings handles its own internal state
  // const handleAvailabilityUpdate = (newStatus) => {
  //   setProfile(prev => ({
  //     ...prev,
  //     is_available_today: newStatus === 'available'
  //   }));
  // };

  if (authLoading || loading) { // Combined loading state
    return (
      <ProtectedRoute>
        <AdminLayout>
          <div className={styles.container}>
            <div className={styles.header}>
              <h1>Artist & Braider Dashboard</h1>
              <p>Loading your personalized dashboard...</p>
            </div>
            <div className={styles.loadingContainer}>
              <div className={styles.spinner}></div>
              <span>{authLoading ? 'Authenticating...' : 'Loading dashboard data...'}</span>
            </div>
          </div>
        </AdminLayout>
      </ProtectedRoute>
    );
  }

  if (error) {
    return (
      <ProtectedRoute>
        <AdminLayout>
          <div className={styles.container}>
            <div className={styles.header}>
              <h1>Artist & Braider Dashboard</h1>
            </div>
            <div className={styles.errorContainer}>
              <div className={styles.errorIcon}>⚠️</div>
              <h2>Access Restricted or Error</h2>
              <p>{error}</p>
              <button 
                onClick={fetchDashboardData}
                className={styles.retryButton}
              >
                Try Again
              </button>
            </div>
          </div>
        </AdminLayout>
      </ProtectedRoute>
    );
  }

  // If profile is still null after loading and no error, it might be due to redirection
  // or an edge case. It's safer to not render the main content.
  if (!profile && !error) { // Check !error as well to avoid showing this during error state
    return (
      <ProtectedRoute>
        <AdminLayout>
          <div className={styles.container}>
            <div className={styles.loadingContainer}>
                <div className={styles.spinner}></div>
                <span>Verifying profile status...</span>
            </div>
          </div>
        </AdminLayout>
      </ProtectedRoute>
    );
  }

  return (
    <ProtectedRoute>
      <AdminLayout>
        <Head>
          <title>Artist Dashboard | Ocean Soul Sparkles</title>
        </Head>
        <div className={styles.container}>
          <div className={styles.header}>
            <div className={styles.headerContent}>
              <h1>Artist & Braider Dashboard</h1>
              <p>Welcome back, {profile?.display_name || profile?.artist_name || user?.email || 'Artist'}!</p>
              {isFirstTimeLogin && (
                <div className={styles.welcomeBanner}>
                  <p>🎉 Welcome to your new dashboard! Complete your profile to get started.</p>
                </div>
              )}
            </div>
            <div className={styles.headerActions}>
              <button
                onClick={fetchDashboardData}
                className={styles.refreshButton}
                title="Refresh Dashboard"
                disabled={loading || authLoading}
              >
                {loading ? 'Refreshing...' : '🔄 Refresh'}
              </button>
              <button
                onClick={async () => {
                  try {
                    await signOut()
                    router.push('/')
                  } catch (error) {
                    console.error('Logout error:', error)
                    toast.error('Failed to sign out')
                  }
                }}
                className={styles.signOutButton}
                title="Sign Out"
              >
                🚪 Sign Out
              </button>
            </div>
          </div>

          <div className={styles.dashboardGrid}>
            {/* Quick Actions Card - Can be kept or modified */}
            <div className={`${styles.card} ${styles.quickActionsSection}`}>
              <QuickActionsCard 
                profile={profile}
                // onProfileUpdate={handleProfileUpdate} // Link will handle this
                userPermissions={dashboardData?.permissions}
              />
            </div>

            {/* Availability Settings Component */}
            <div className={`${styles.card} ${styles.availabilitySection}`}>
              {/* Pass artistProfile to AvailabilitySettings if it needs it directly,
                  otherwise it can use useAuth to get user and then artistProfile.id */}
              <AvailabilitySettings />
            </div>

            {/* Booking List Card - Placeholder */}
            <div className={`${styles.card} ${styles.bookingsSection}`}>
              {/* <h3>Upcoming & Past Bookings</h3> // Title is now inside BookingListCard */}
              {profile && profile.id ? (
                <BookingListCard artistId={profile.id} />
              ) : (
                <p>Loading booking information...</p>
              )}
            </div>

            {/* Performance Metrics Card - Can be kept or modified */}
            <div className={`${styles.card} ${styles.metricsSection}`}>
              <PerformanceMetricsCard 
                profile={profile} // Pass full profile
                stats={dashboardData?.stats} // Pass stats from dashboardData
              />
            </div>
          </div>

          {/* Profile Summary - can be kept or simplified if data is in QuickActions or ProfileManagementCard */}
          <div className={`${styles.card} ${styles.profileSummary}`}>
            {/* <div className={styles.summaryCard}> // Redundant if .card is used */}
              <h3>Profile Summary</h3>
              <div className={styles.summaryGrid}>
                <div className={styles.summaryItem}>
                  <span className={styles.summaryLabel}>Role</span>
                  <span className={styles.summaryValue}>
                    {user?.role?.charAt(0).toUpperCase() + user?.role?.slice(1) || 'N/A'}
                  </span>
                </div>
                <div className={styles.summaryItem}>
                  <span className={styles.summaryLabel}>Specialties</span>
                  <span className={styles.summaryValue}>
                    {profile?.specializations?.join(', ') || 'Not set'}
                  </span>
                </div>
                {/* Removed Experience from summary as it's in ProfileManagementCard
                <div className={styles.summaryItem}>
                  <span className={styles.summaryLabel}>Experience</span>
                  <span className={styles.summaryValue}>
                    {profile?.experience_years || 0} years
                  </span>
                </div>
                */}
                <div className={styles.summaryItem}>
                  <span className={styles.summaryLabel}>Status Today</span>
                  <span 
                    className={styles.summaryValue}
                    style={{ 
                      color: profile?.is_available_today ? '#10b981' : '#ef4444',
                      fontWeight: 'bold'
                    }}
                  >
                    {profile?.is_available_today ? 'Available' : 'Unavailable'}
                  </span>
                </div>
                 <div className={styles.summaryItem}>
                  <span className={styles.summaryLabel}>Profile Complete</span>
                  <span className={styles.summaryValue} style={{color: profile?.is_profile_complete ? 'green' : 'orange'}}>
                    {profile?.is_profile_complete ? 'Yes' :
                      <Link href="/admin/complete-profile" legacyBehavior><a className={styles.completeProfileLink}>No, Complete Now</a></Link>
                    }
                  </span>
                </div>
              </div>
            {/* </div> */}
          </div>

          {/* Help Section - Updated Profile Link */}
          <div className={styles.helpSection}>
            <div className={styles.helpCard}>
              <h3>Need Help?</h3>
              <div className={styles.helpLinks}>
                <a href="/help/artist-guide" target="_blank" rel="noopener noreferrer" className={styles.helpLink}>
                  📖 Artist Guide
                </a>
                <a href="/help/booking-management" target="_blank" rel="noopener noreferrer" className={styles.helpLink}>
                  📅 Booking Management
                </a>
                <a href="/help/contact" target="_blank" rel="noopener noreferrer" className={styles.helpLink}>
                  💬 Contact Support
                </a>
                <Link href="/admin/my-profile" legacyBehavior>
                  <a className={styles.helpLink}>👤 Update Profile</a>
                </Link>
              </div>
            </div>
          </div>
        </div>
      </AdminLayout>
    </ProtectedRoute>
  )
}
